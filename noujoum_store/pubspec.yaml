name: noujoum_store
description: "Noujoum Store - L'innovation mauritanienne à portée de main"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Core dependencies for Noujoum Store - UPDATED VERSIONS
  shared_preferences: ^2.3.2      # For favorites and preferences (updated from 2.2.0)
  url_launcher: ^6.3.1            # For contact links (updated from 6.1.12)
  share_plus: ^10.1.2             # For sharing functionality (updated from 7.1.0)
  cached_network_image: ^3.4.1    # For image caching (updated from 3.2.3)
  flutter_staggered_grid_view: ^0.7.0  # Latest stable version
  
  # New marketplace dependencies - UPDATED VERSIONS
  image_picker: ^1.1.2            # For app screenshots upload (updated from 1.0.4)
  video_player: ^2.9.2            # For demo videos (updated from 2.7.0)
  file_picker: ^8.1.3             # For document uploads (DOWNGRADED from 10.3.2 - this was causing the iOS issue)
  flutter_rating_bar: ^4.0.1      # Latest stable version
  carousel_slider: ^5.0.0         # For image galleries (updated from 4.2.1)
  expandable: ^5.0.1              # Latest stable version
  flutter_html: ^3.0.0            # Latest stable version (note: consider html_widget ^0.15.0 as alternative)
  table_calendar: ^3.1.2          # For demo scheduling (updated from 3.0.9)
  flutter_form_builder: ^9.4.1    # For complex forms (updated from 9.1.1)
  flutter_launcher_icons: ^0.13.1

  # API and networking dependencies
  http: ^1.2.2                    # For HTTP requests
  dio: ^5.7.0                     # Advanced HTTP client
  json_annotation: ^4.9.0        # For JSON serialization

flutter_launcher_icons:
  android: true
  ios: false
  image_path: "assets/images/noujoum_store_logo.png"


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0           # Latest stable version

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package