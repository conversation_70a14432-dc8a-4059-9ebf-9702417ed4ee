<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\App;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AppController extends Controller
{
    /**
     * Display a listing of apps with filtering and pagination
     */
    public function index(Request $request): JsonResponse
    {
        $query = App::with(['user', 'category'])->approved();

        // Apply filters
        if ($request->has('category')) {
            $query->byCategory($request->category);
        }

        if ($request->has('search')) {
            $query->search($request->search);
        }

        if ($request->has('featured')) {
            $query->featured();
        }

        if ($request->has('verified')) {
            $query->verified();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $apps = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $apps
        ]);
    }

    /**
     * Store a newly created app
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'app_name' => 'required|string|max:255',
            'tagline' => 'required|string|max:255',
            'description' => 'required|string',
            'detailed_description' => 'nullable|string',
            // Developer info will be taken from authenticated user
            'developer_whatsapp' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
            'developer_website' => 'nullable|url|max:255',
            'app_type' => 'required|in:mobile,web,desktop,saas,api,plugin,template',
            'supported_platforms' => 'required|array',
            'current_version' => 'nullable|string|max:50',
            // Accept either file upload or URL for icon
            'icon_file' => 'nullable|file|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'icon_url' => 'nullable',
            // Accept either file uploads or URLs for screenshots
            'screenshot_files' => 'nullable|array|max:10',
            'screenshot_files.*' => 'file|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'screenshots' => 'nullable|array|min:1',
            'screenshots.*' => 'string',
            'demo_videos' => 'nullable|array',
            'demo_videos.*' => 'url',
            'live_demo' => 'nullable|url',
            'download_link' => 'nullable|url',
            'license_type' => 'required|in:oneTime,monthly,yearly,custom,free',
            'pricing_model' => 'required|in:free,freemium,paid,enterprise,custom',
            'pricing' => 'required|string|max:255',
            'has_free_trial' => 'boolean',
            'trial_days' => 'nullable|integer|min:0',
            'is_open_source' => 'boolean',
            'target_audience' => 'required|string|max:255',
            'business_sectors' => 'required|array',
            'business_value' => 'required|string',
            'key_features' => 'required|array',
            'technical_requirements' => 'nullable|string',
            'has_documentation' => 'boolean',
            'documentation_url' => 'nullable|url',
            'support_options' => 'required|array',
            'languages' => 'required|array',
            'category_id' => 'nullable',
            'subcategory' => 'required|string|max:255',
            'tags' => 'required|array',
            'search_keywords' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Custom validation: ensure either icon_file or icon_url is provided
        if (!$request->hasFile('icon_file') && !$request->filled('icon_url')) {
            return response()->json([
                'success' => false,
                'message' => 'Either icon file or icon URL is required'
            ], 422);
        }

        // Custom validation: ensure either screenshot_files or screenshots URLs are provided
        if (!$request->hasFile('screenshot_files') && !$request->filled('screenshots')) {
            return response()->json([
                'success' => false,
                'message' => 'Either screenshot files or screenshot URLs are required'
            ], 422);
        }

        // Get authenticated user info
        $user = $request->user();

        // Handle file uploads
        $iconUrl = $request->get('icon_url');
        $screenshots = $request->get('screenshots', []);

        // Upload icon if file is provided
        if ($request->hasFile('icon_file')) {
            $iconFile = $request->file('icon_file');
            $iconFilename = time() . '_icon_' . \Str::random(10) . '.' . $iconFile->getClientOriginalExtension();
            $iconPath = $iconFile->storeAs('app-icons', $iconFilename, 'public');
            $iconUrl = \Storage::url($iconPath);
        }

        // Upload screenshots if files are provided
        if ($request->hasFile('screenshot_files')) {
            $screenshots = [];
            foreach ($request->file('screenshot_files') as $index => $screenshotFile) {
                $screenshotFilename = time() . '_screenshot_' . $index . '_' . \Str::random(10) . '.' . $screenshotFile->getClientOriginalExtension();
                $screenshotPath = $screenshotFile->storeAs('app-screenshots', $screenshotFilename, 'public');
                $screenshots[] = \Storage::url($screenshotPath);
            }
        }

        // Prepare validated data and override with processed file URLs
        $validatedData = $validator->validated();
        $validatedData['icon_url'] = $iconUrl;
        $validatedData['screenshots'] = $screenshots;

        // Remove file fields from validated data as they're not database columns
        unset($validatedData['icon_file']);
        unset($validatedData['screenshot_files']);


        // if category_id null make it 1
        $validatedData['category_id'] = $request->get('category_id') ?? 1;
        $app = App::create(array_merge($validatedData, [
            'user_id' => $user->id,
            // Populate developer info from authenticated user
            'developer_name' => $user->name,
            'developer_email' => $user->email,
            'developer_phone' => $user->phone ?? '',
            'publish_date' => now(),
            'is_approved' => false,
        ]));

        return response()->json([
            'success' => true,
            'message' => 'App submitted successfully and is pending approval',
            'data' => $app->load(['user', 'category'])
        ], 201);
    }

    /**
     * Display the specified app
     */
    public function show(string $id): JsonResponse
    {
        $app = App::with(['user', 'category'])->findOrFail($id);
        
        $app->increment('view_count');

        return response()->json([
            'success' => true,
            'data' => $app
        ]);
    }

    /**
     * Update the specified app
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $app = App::findOrFail($id);

        if ($app->user_id !== $request->user()->id && !$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to update this app'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'app_name' => 'required|string|max:255',
            'tagline' => 'required|string|max:255',
            'description' => 'required|string',
            'detailed_description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'subcategory' => 'required|string|max:255',
            'tags' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $app->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'App updated successfully',
            'data' => $app->load(['user', 'category'])
        ]);
    }

    /**
     * Remove the specified app
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        $app = App::findOrFail($id);

        if ($app->user_id !== $request->user()->id && !$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to delete this app'
            ], 403);
        }

        $app->delete();

        return response()->json([
            'success' => true,
            'message' => 'App deleted successfully'
        ]);
    }

    /**
     * Get user's own apps
     */
    public function myApps(Request $request): JsonResponse
    {
        $apps = App::with('category')
            ->where('user_id', $request->user()->id)
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $apps
        ]);
    }

    /**
     * Get featured apps
     */
    public function featured(): JsonResponse
    {
        $apps = App::with(['user', 'category'])
            ->approved()
            ->featured()
            ->orderBy('rating', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $apps
        ]);
    }

    /**
     * Get top rated apps
     */
    public function topRated(): JsonResponse
    {
        $apps = App::with(['user', 'category'])
            ->approved()
            ->orderBy('rating', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $apps
        ]);
    }

    /**
     * Get most downloaded apps
     */
    public function mostDownloaded(): JsonResponse
    {
        $apps = App::with(['user', 'category'])
            ->approved()
            ->orderBy('downloads', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $apps
        ]);
    }

    /**
     * Get marketplace statistics
     */
    public function stats(): JsonResponse
    {
        $totalApps = App::approved()->count();
        $totalDevelopers = App::approved()->distinct('user_id')->count('user_id');
        $totalDownloads = App::approved()->sum('downloads');

        return response()->json([
            'success' => true,
            'data' => [
                'total_apps' => $totalApps,
                'total_developers' => $totalDevelopers,
                'total_downloads' => $totalDownloads,
                'satisfied_clients' => '1000+', // This could be calculated from reviews/ratings in the future
            ]
        ]);
    }
}
